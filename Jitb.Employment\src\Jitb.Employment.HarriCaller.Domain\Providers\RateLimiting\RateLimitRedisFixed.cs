using NLog;
using StackExchange.Redis;
using System;
using System.Threading.Tasks;

namespace Jitb.Employment.HarriCaller.Domain.Providers.RateLimiting
{
    /// <summary>
    /// Fixed Redis-based rate limiter that uses high-precision timestamps and unique member values
    /// to prevent overwrites in Redis sorted sets and ensure correct rate limiting.
    /// </summary>
    public class RateLimitRedisFixed : IRateLimit, IDisposable
    {
        private readonly ConnectionMultiplexer _redis;
        private readonly IDatabase _database;
        private readonly ILogger _log;
        private readonly int _maxRequestsPerMinute;
        private readonly string _keyPrefix;

        /// <summary>
        /// Initializes a new instance of the <see cref="RateLimitRedisFixed"/> class.
        /// </summary>
        /// <param name="connectionString">Redis connection string</param>
        /// <param name="log">Logger instance</param>
        /// <param name="maxRequestsPerMinute">Maximum number of requests per minute (default: 200)</param>
        /// <param name="keyPrefix">Prefix for Redis keys (default: "harri-rate-limit:")</param>
        public RateLimitRedisFixed(string connectionString, ILogger log, int maxRequestsPerMinute = 200, string keyPrefix = "harri-rate-limit:")
        {
            _log = log ?? throw new ArgumentNullException(nameof(log));
            _maxRequestsPerMinute = maxRequestsPerMinute;
            _keyPrefix = keyPrefix ?? throw new ArgumentNullException(nameof(keyPrefix));
            
            try
            {
                _redis = ConnectionMultiplexer.Connect(connectionString);
                _database = _redis.GetDatabase();
                _log.Info($"Connected to Redis for rate limiting: {connectionString}");
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Failed to connect to Redis: {connectionString}");
                throw;
            }
        }

        /// <summary>
        /// Checks if a request can be made based on the rate limit for the given key.
        /// </summary>
        /// <param name="key">The rate limit key (e.g., "api-endpoint")</param>
        /// <returns>True if the request is allowed, false if rate limit exceeded</returns>
        public async Task<bool> CanMakeRequestAsync(string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("Key cannot be null or empty", nameof(key));

            var fullKey = _keyPrefix + key;
            var now = DateTimeOffset.UtcNow;
            var windowStart = now.AddMinutes(-1);

            try
            {
                // Remove expired entries (older than 1 minute)
                await _database.SortedSetRemoveRangeByScoreAsync(fullKey, 0, windowStart.Ticks);

                // Count current requests in the window
                var currentCount = await _database.SortedSetLengthAsync(fullKey);

                _log.Debug($"Rate limit check for {fullKey}: {currentCount}/{_maxRequestsPerMinute} requests in the last minute");

                // Check if we're at or over the limit
                if (currentCount >= _maxRequestsPerMinute)
                {
                    _log.Debug($"Rate limit exceeded for {fullKey}: {currentCount}/{_maxRequestsPerMinute}");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Error checking rate limit for key {fullKey}");
                // In case of Redis errors, allow the request to proceed to avoid blocking the application
                return true;
            }
        }

        /// <summary>
        /// Checks if a request can be made based on the rate limit for the given key.
        /// Uses high-precision timestamps and unique member values to prevent overwrites.
        /// This method is thread-safe and uses a Lua script to atomically check and record the request.
        /// </summary>
        /// <param name="key">The rate limit key (e.g., "api-endpoint")</param>
        /// <returns>True if the request is allowed, false if rate limit exceeded</returns>
        public async Task<bool> TryMakeRequestAsync(string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("Key cannot be null or empty", nameof(key));

            var fullKey = _keyPrefix + key;
            var now = DateTimeOffset.UtcNow;
            var windowStart = now.AddMinutes(-1);

            try
            {
                // Remove expired entries (older than 1 minute)
                await _database.SortedSetRemoveRangeByScoreAsync(fullKey, 0, windowStart.Ticks);

                // Use high-precision timestamp (ticks) to prevent collisions
                var timestampTicks = now.Ticks;

                // Create a unique member by combining timestamp with a random component
                // This ensures no two requests overwrite each other in the sorted set
                var uniqueMember = $"{timestampTicks}:{Guid.NewGuid():N}";

                // Why use a Lua script here?
                // -------------------------------------------------------------
                // The Lua script is used to ensure the rate limiting operation is
                // ATOMIC, CONSISTENT, and PERFORMANT. By running the check, cleanup,
                // and record steps as a single script on the Redis server, we:
                //   1. Prevent race conditions (atomicity): No two requests can "see"
                //      the same count and both pass the limit at the same time.
                //   2. Reduce network round-trips (performance): All logic is executed
                //      in one call, not multiple.
                //   3. Guarantee correct behavior under concurrency (consistency):
                //      The rate limit is always enforced correctly, even with many
                //      simultaneous requests.
                // This is much safer and more reliable than using separate
                // CanMakeRequestAsync and RecordRequestAsync calls.
                const string luaScript = @"
                    local key = KEYS[1]
                    local windowStart = tonumber(ARGV[1])
                    local timestampTicks = tonumber(ARGV[2])
                    local uniqueMember = ARGV[3]
                    local maxRequests = tonumber(ARGV[4])
                    local expireSeconds = tonumber(ARGV[5])
                    
                    -- Remove expired entries (older than 1 minute)
                    redis.call('ZREMRANGEBYSCORE', key, 0, windowStart)
                    
                    -- Count current requests in the window
                    local currentCount = redis.call('ZCARD', key)
                    
                    -- Check if we're at or over the limit
                    if currentCount >= maxRequests then
                        return 0  -- Rate limit exceeded
                    end
                    
                    -- Add the current request
                    redis.call('ZADD', key, timestampTicks, uniqueMember)
                    
                    -- Set expiration to prevent memory leaks
                    redis.call('EXPIRE', key, expireSeconds)
                    
                    return 1  -- Request allowed
                ";

                var result = await _database.ScriptEvaluateAsync(luaScript,
                    new RedisKey[] { fullKey },
                    new RedisValue[] {
                        windowStart.Ticks,
                        timestampTicks,
                        uniqueMember,
                        _maxRequestsPerMinute,
                        120  // 2 minutes expiration in seconds
                    });

                var allowed = (int)result == 1;

                if (allowed)
                {
                    _log.Debug($"Request allowed for {fullKey} at {timestampTicks}");
                }
                else
                {
                    _log.Debug($"Rate limit exceeded for {fullKey} (max: {_maxRequestsPerMinute})");
                }

                return allowed;
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Error checking rate limit for key {fullKey}");
                // In case of Redis errors, allow the request to proceed to avoid blocking the application
                return true;
            }
        }

        /// <summary>
        /// Records a request for the given key in the rate limiter.
        /// </summary>
        /// <param name="key">The rate limit key (e.g., "api-endpoint")</param>
        /// <returns>A task that completes when the request is recorded</returns>
        public async Task RecordRequestAsync(string key)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("Key cannot be null or empty", nameof(key));

            var fullKey = _keyPrefix + key;
            var now = DateTimeOffset.UtcNow;

            try
            {
                // Use high-precision timestamp (ticks) to prevent collisions
                var timestampTicks = now.Ticks;

                // Create a unique member by combining timestamp with a random component
                // This ensures no two requests overwrite each other in the sorted set
                var uniqueMember = $"{timestampTicks}:{Guid.NewGuid():N}";

                // Add the current request to the sorted set
                await _database.SortedSetAddAsync(fullKey, uniqueMember, timestampTicks);

                // Set expiration to prevent memory leaks (2 minutes)
                await _database.KeyExpireAsync(fullKey, TimeSpan.FromMinutes(2));

                _log.Debug($"Recorded request for {fullKey} at {timestampTicks}");
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Error recording request for key {fullKey}: {ex.Message}");
            }
        }

        /// <summary>
        /// Disposes the Redis connection
        /// </summary>
        public void Dispose()
        {
            _redis?.Dispose();
        }
    }
}
